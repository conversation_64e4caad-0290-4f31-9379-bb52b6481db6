<?php
/**
 * Plugin Name: Email Novo Usuário
 * Description: Plugin personalizado para customizar o email de criação de novo usuário do WordPress com design white label e templates de email
 * Version: 1.0.0
 * Author: Astra Child Theme
 */

if (!defined('ABSPATH')) {
    exit('Acesso direto não permitido');
}

// Incluir arquivo de instalação
require_once __DIR__ . '/install.php';

class CustomNewUserEmail {
    
    private $plugin_path;
    private $plugin_url;
    
    public function __construct() {
        $this->plugin_path = plugin_dir_path(__FILE__);
        $this->plugin_url = plugin_dir_url(__FILE__);

        // Executar instalação/upgrade se necessário
        CustomNewUserEmailInstaller::upgrade();

        $this->init_hooks();
    }
    
    private function init_hooks() {
        // Menu de administração
        add_action('admin_menu', [$this, 'add_admin_menu']);
        add_action('admin_init', [$this, 'register_settings']);
        
        // Enfileirar scripts e estilos administrativos
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);
        
        // AJAX handlers
        add_action('wp_ajax_cnue_save_settings', [$this, 'save_settings_ajax']);
        add_action('wp_ajax_cnue_reset_settings', [$this, 'reset_settings_ajax']);
        add_action('wp_ajax_cnue_send_test_email', [$this, 'send_test_email_ajax']);
        add_action('wp_ajax_cnue_generate_email_preview', [$this, 'generate_email_preview_ajax']);
        
        // Hooks para personalização de email de novo usuário
        add_filter('wp_new_user_notification_email', [$this, 'custom_new_user_notification_email'], 10, 3);
        add_filter('wp_mail_content_type', [$this, 'set_html_content_type']);
        add_filter('wp_mail_from', [$this, 'custom_new_user_from_email'], 20);
        add_filter('wp_mail_from_name', [$this, 'custom_new_user_from_name'], 20);
    }

    public function enqueue_admin_assets($hook) {
        // Só carrega na página do plugin
        if ($hook !== 'settings_page_custom-new-user-email') {
            return;
        }

        // Enfileirar recursos necessários do WordPress
        wp_enqueue_style('wp-color-picker');
        wp_enqueue_script('wp-color-picker');
        wp_enqueue_script('jquery');
        wp_enqueue_media();
    }
    
    private function get_settings() {
        return [
            'theme' => get_option('cnue_theme', 'dark'),
            'logo_url' => get_option('cnue_logo_url', ''),

            // Configurações de email
            'email_enabled' => get_option('cnue_email_enabled', false),
            'email_subject' => get_option('cnue_email_subject', 'Bem-vindo! Seus dados de acesso'),
            'email_from_name' => get_option('cnue_email_from_name', get_bloginfo('name')),
            'email_from_email' => get_option('cnue_email_from_email', get_option('admin_email')),
            'email_header_color' => get_option('cnue_email_header_color', '#4a90e2'),
            'email_header_pattern' => get_option('cnue_email_header_pattern', 'none'),
            'email_button_color' => get_option('cnue_email_button_color', '#4a90e2'),
            'email_footer_text' => get_option('cnue_email_footer_text', 'Este é um email automático, não responda.'),
            'email_greeting' => get_option('cnue_email_greeting', 'Olá'),
            'email_main_text' => get_option('cnue_email_main_text', 'Sua conta foi criada com sucesso em {site_name}! Abaixo estão seus dados de acesso:'),
            'email_instruction_text' => get_option('cnue_email_instruction_text', 'Clique no botão abaixo para fazer login e começar a usar sua conta:'),
            'email_button_text' => get_option('cnue_email_button_text', 'Fazer Login'),
            'email_credentials_title' => get_option('cnue_email_credentials_title', 'Seus dados de acesso:'),
            'email_username_label' => get_option('cnue_email_username_label', 'Usuário:'),
            'email_password_label' => get_option('cnue_email_password_label', 'Senha:'),
            'email_login_url_label' => get_option('cnue_email_login_url_label', 'Link de acesso:'),
            'email_security_note' => get_option('cnue_email_security_note', 'Por segurança, recomendamos que você altere sua senha após o primeiro login.')
        ];
    }
    
    public function add_admin_menu() {
        add_options_page(
            'Email Novo Usuário',
            'Email Novo Usuário',
            'manage_options',
            'custom-new-user-email',
            [$this, 'admin_page']
        );
    }
    
    public function register_settings() {
        $settings = [
            'cnue_theme', 'cnue_logo_url',
            // Configurações de email
            'cnue_email_enabled', 'cnue_email_subject', 'cnue_email_from_name', 'cnue_email_from_email',
            'cnue_email_header_color', 'cnue_email_header_pattern', 'cnue_email_button_color', 'cnue_email_footer_text',
            'cnue_email_greeting', 'cnue_email_main_text', 'cnue_email_instruction_text', 'cnue_email_button_text',
            'cnue_email_credentials_title', 'cnue_email_username_label', 'cnue_email_password_label', 
            'cnue_email_login_url_label', 'cnue_email_security_note'
        ];

        foreach ($settings as $setting) {
            register_setting('cnue_settings', $setting);
        }
    }
    
    public function admin_page() {
        $settings = $this->get_settings();
        include $this->plugin_path . 'templates/admin-page.php';
    }
    
    public function save_settings_ajax() {
        check_ajax_referer('cnue_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $settings = $_POST['settings'];
        $is_preview = isset($_POST['preview']) && $_POST['preview'] === '1';

        // Checkboxes que precisam de tratamento especial
        $checkboxes = ['email_enabled'];
        
        // Primeiro, resetar checkboxes
        foreach ($checkboxes as $checkbox) {
            update_option('cnue_' . $checkbox, false);
        }

        // Processar os valores enviados
        foreach ($settings as $key => $value) {
            // Sanitizar valor baseado no tipo
            if (in_array($key, ['main_text', 'instruction_text', 'security_note'])) {
                $sanitized_value = wp_kses_post($value);
            } elseif (in_array($key, $checkboxes)) {
                // Para checkboxes, converter para boolean
                $sanitized_value = ($value === '1' || $value === 'on') ? true : false;
            } else {
                $sanitized_value = sanitize_text_field($value);
            }

            update_option('cnue_' . $key, $sanitized_value);
        }

        // Limpar cache se necessário
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }

        $message = $is_preview ? 'Configurações aplicadas ao preview!' : 'Configurações salvas com sucesso!';
        wp_send_json_success($message);
    }
    
    public function reset_settings_ajax() {
        check_ajax_referer('cnue_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }
        
        $defaults = [
            'cnue_theme' => 'dark',
            'cnue_logo_url' => '',
            'cnue_email_enabled' => false,
            'cnue_email_subject' => 'Bem-vindo! Seus dados de acesso',
            'cnue_email_from_name' => get_bloginfo('name'),
            'cnue_email_from_email' => get_option('admin_email'),
            'cnue_email_header_color' => '#4a90e2',
            'cnue_email_header_pattern' => 'none',
            'cnue_email_button_color' => '#4a90e2',
            'cnue_email_footer_text' => 'Este é um email automático, não responda.',
            'cnue_email_greeting' => 'Olá',
            'cnue_email_main_text' => 'Sua conta foi criada com sucesso em {site_name}! Abaixo estão seus dados de acesso:',
            'cnue_email_instruction_text' => 'Clique no botão abaixo para fazer login e começar a usar sua conta:',
            'cnue_email_button_text' => 'Fazer Login',
            'cnue_email_credentials_title' => 'Seus dados de acesso:',
            'cnue_email_username_label' => 'Usuário:',
            'cnue_email_password_label' => 'Senha:',
            'cnue_email_login_url_label' => 'Link de acesso:',
            'cnue_email_security_note' => 'Por segurança, recomendamos que você altere sua senha após o primeiro login.'
        ];
        
        foreach ($defaults as $key => $value) {
            update_option($key, $value);
        }
        
        wp_send_json_success('Configurações resetadas com sucesso!');
    }

    /**
     * Personaliza o email de novo usuário
     */
    public function custom_new_user_notification_email($wp_new_user_notification_email, $user, $blogname) {
        // Verificar se a personalização está habilitada
        if (!get_option('cnue_email_enabled', false)) {
            return $wp_new_user_notification_email;
        }

        $login_url = wp_login_url();
        
        // Carregar template de email
        $template_path = $this->plugin_path . 'templates/email-template.php';
        if (file_exists($template_path)) {
            ob_start();
            include $template_path;
            $html_message = ob_get_clean();
            
            $wp_new_user_notification_email['subject'] = get_option('cnue_email_subject', 'Bem-vindo! Seus dados de acesso');
            $wp_new_user_notification_email['message'] = $html_message;
            $wp_new_user_notification_email['headers'] = ['Content-Type: text/html; charset=UTF-8'];
        }

        return $wp_new_user_notification_email;
    }

    /**
     * Define o tipo de conteúdo como HTML para emails
     */
    public function set_html_content_type() {
        return 'text/html';
    }

    /**
     * Personaliza o email do remetente
     */
    public function custom_new_user_from_email($email) {
        // Só aplicar se a personalização estiver habilitada e se for email de novo usuário
        if (!get_option('cnue_email_enabled', false) || !$this->is_new_user_email()) {
            return $email;
        }

        $custom_email = get_option('cnue_email_from_email', '');
        if (!empty($custom_email) && is_email($custom_email)) {
            return $custom_email;
        }
        return $email;
    }

    /**
     * Personaliza o nome do remetente
     */
    public function custom_new_user_from_name($name) {
        // Só aplicar se a personalização estiver habilitada e se for email de novo usuário
        if (!get_option('cnue_email_enabled', false) || !$this->is_new_user_email()) {
            return $name;
        }

        $custom_name = get_option('cnue_email_from_name', '');
        if (!empty($custom_name)) {
            return $custom_name;
        }
        return $name;
    }

    /**
     * Verifica se o email atual é de novo usuário
     */
    private function is_new_user_email() {
        // Verificar se estamos no contexto de criação de novo usuário
        $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 10);

        foreach ($backtrace as $trace) {
            if (isset($trace['function']) &&
                ($trace['function'] === 'wp_new_user_notification' ||
                 $trace['function'] === 'wp_send_new_user_notifications')) {
                return true;
            }
        }

        return false;
    }

    /**
     * Envia email de teste via AJAX
     */
    public function send_test_email_ajax() {
        check_ajax_referer('cnue_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $test_email = sanitize_email($_POST['email']);
        if (!is_email($test_email)) {
            wp_send_json_error('Endereço de email inválido.');
        }

        // Criar dados fictícios para o teste
        $user = (object) [
            'display_name' => 'Usuário Teste',
            'user_email' => $test_email,
            'user_login' => 'teste',
            'user_pass' => 'senha123'
        ];

        $login_url = wp_login_url();

        // Carregar template de email
        $template_path = $this->plugin_path . 'templates/email-template.php';
        if (file_exists($template_path)) {
            ob_start();
            include $template_path;
            $html_message = ob_get_clean();

            $subject = get_option('cnue_email_subject', 'Bem-vindo! Seus dados de acesso - TESTE');
            $from_name = get_option('cnue_email_from_name', get_bloginfo('name'));
            $from_email = get_option('cnue_email_from_email', get_option('admin_email'));

            $headers = [
                'Content-Type: text/html; charset=UTF-8',
                'From: ' . $from_name . ' <' . $from_email . '>'
            ];

            $sent = wp_mail($test_email, $subject . ' (TESTE)', $html_message, $headers);

            if ($sent) {
                wp_send_json_success('Email de teste enviado com sucesso!');
            } else {
                wp_send_json_error('Erro ao enviar email de teste.');
            }
        } else {
            wp_send_json_error('Template de email não encontrado.');
        }
    }

    /**
     * Gera preview do email via AJAX
     */
    public function generate_email_preview_ajax() {
        check_ajax_referer('cnue_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $preview_settings = $_POST['settings'];
        
        // Temporariamente salvar as configurações para o preview
        $original_settings = [];
        foreach ($preview_settings as $key => $value) {
            $option_key = 'cnue_' . $key;
            $original_settings[$key] = get_option($option_key);
            update_option($option_key, sanitize_text_field($value));
        }

        // Criar dados fictícios para o preview
        $user = (object) [
            'display_name' => 'João Silva',
            'user_email' => '<EMAIL>',
            'user_login' => 'joao',
            'user_pass' => 'minhasenha123'
        ];

        $login_url = 'https://seusite.com/wp-login.php';

        // Carregar template de email
        $template_path = $this->plugin_path . 'templates/email-template.php';
        if (file_exists($template_path)) {
            ob_start();
            include $template_path;
            $html_preview = ob_get_clean();

            // Restaurar configurações originais
            foreach ($original_settings as $key => $value) {
                $option_key = 'cnue_' . $key;
                update_option($option_key, $value);
            }

            wp_send_json_success($html_preview);
        } else {
            wp_send_json_error('Template de email não encontrado.');
        }
    }
}

// Inicializar o plugin
new CustomNewUserEmail();
