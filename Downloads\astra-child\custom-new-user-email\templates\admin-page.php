<?php
/**
 * Página de Administração do Custom New User Email Plugin
 */

if (!defined('ABSPATH')) {
    exit;
}

// Verificar permissões
if (!current_user_can('manage_options')) {
    wp_die('Você não tem permissão para acessar esta página.');
}

$nonce = wp_create_nonce('cnue_admin_nonce');

// Obter configurações atuais
$settings = [
    'email_enabled' => get_option('cnue_email_enabled', '0'),
    'email_subject' => get_option('cnue_email_subject', 'Bem-vindo! Seus dados de acesso'),
    'email_from_name' => get_option('cnue_email_from_name', get_bloginfo('name')),
    'email_from_email' => get_option('cnue_email_from_email', get_option('admin_email')),
    'email_header_color' => get_option('cnue_email_header_color', '#4a90e2'),
    'email_header_pattern' => get_option('cnue_email_header_pattern', 'none'),
    'email_button_color' => get_option('cnue_email_button_color', '#4a90e2'),
    'email_greeting' => get_option('cnue_email_greeting', 'Olá'),
    'email_main_text' => get_option('cnue_email_main_text', 'Sua conta foi criada com sucesso em {site_name}! Abaixo estão seus dados de acesso:'),
    'email_credentials_title' => get_option('cnue_email_credentials_title', 'Seus dados de acesso:'),
    'email_username_label' => get_option('cnue_email_username_label', 'Usuário:'),
    'email_password_label' => get_option('cnue_email_password_label', 'Senha:'),
    'email_login_url_label' => get_option('cnue_email_login_url_label', 'Link de acesso:'),
    'email_instruction_text' => get_option('cnue_email_instruction_text', 'Clique no botão abaixo para fazer login e começar a usar sua conta:'),
    'email_button_text' => get_option('cnue_email_button_text', 'Fazer Login'),
    'email_security_note' => get_option('cnue_email_security_note', 'Por segurança, recomendamos que você altere sua senha após o primeiro login.'),
    'email_footer_text' => get_option('cnue_email_footer_text', 'Este é um email automático, não responda.')
];
?>

<style>
/* Custom New User Email Admin Styles - Copiado do Custom Password Reset */

/* Garantir que html e body permitam sticky */
html, body {
    height: auto !important;
    overflow-x: hidden !important;
    overflow-y: auto !important;
}

/* WordPress admin body */
body.wp-admin {
    height: auto !important;
    min-height: 100vh !important;
}

/* Container principal do WordPress */
#wpwrap, #wpcontent, #wpbody, #wpbody-content {
    height: auto !important;
    min-height: 100vh !important;
    overflow: visible !important;
}

.cpr-admin-wrap {
    max-width: 100%;
    margin: 0;
    padding: 0;
    background: #f8fafc;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

/* Header moderno */
.cpr-admin-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 0;
    padding: 0;
    margin: 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.cpr-admin-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.08)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.12)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.cpr-header-content {
    position: relative;
    z-index: 2;
    padding: 40px 60px;
}

.cpr-header-title {
    display: flex;
    align-items: center;
    gap: 20px;
}

.cpr-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.cpr-icon .dashicons {
    font-size: 28px;
    color: white;
    width: 28px;
    height: 28px;
}

.cpr-title-text h1 {
    margin: 0 0 8px 0;
    font-size: 32px;
    font-weight: 700;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.cpr-title-text p {
    margin: 0;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 400;
}

/* Container principal */
.cpr-admin-container {
    background: white;
    border: none;
    border-radius: 0;
    margin: 0;
    box-shadow: none;
    overflow: hidden;
}

/* Navigation Tabs - Design moderno */
.cpr-nav-tabs {
    background: white;
    border-bottom: 1px solid #e2e8f0;
    margin: 0;
    padding: 0;
    position: sticky;
    top: 32px;
    z-index: 100;
}

.cpr-nav-wrapper {
    display: flex;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 60px;
    gap: 0;
}

.cpr-nav-tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 24px;
    text-decoration: none;
    color: #64748b;
    font-weight: 500;
    font-size: 14px;
    border-bottom: 3px solid transparent;
    transition: all 0.2s ease;
    position: relative;
    background: none;
    border-left: none;
    border-right: none;
    border-top: none;
}

.cpr-nav-tab:hover {
    color: #475569;
    background: #f8fafc;
    text-decoration: none;
}

.cpr-nav-tab.cpr-nav-tab-active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
    background: white;
}

.cpr-tab-icon {
    font-size: 16px;
    line-height: 1;
}

.cpr-tab-text {
    font-weight: 500;
}

/* Tab Content - Layout moderno */
.cpr-tab-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 60px;
    min-height: 600px;
    background: #f8fafc;
    display: none;
}

.cpr-tab-content.active {
    display: block;
}

/* Grid de configurações */
.cpr-settings-grid {
    display: grid;
    gap: 32px;
}

.cpr-setting-group {
    background: white;
    border-radius: 12px;
    padding: 32px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.cpr-setting-header {
    margin-bottom: 32px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e2e8f0;
}

.cpr-setting-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Setting Items - Design moderno */
.cpr-setting-item {
    margin-bottom: 32px;
}

.cpr-setting-item:last-child {
    margin-bottom: 0;
}

.cpr-setting-label {
    display: block;
    font-weight: 600;
    font-size: 14px;
    color: #374151;
    margin-bottom: 8px;
}

.cpr-setting-desc {
    display: block;
    font-size: 13px;
    color: #6b7280;
    margin-top: 6px;
    line-height: 1.5;
}

/* Inputs modernos */
.cpr-input, .cpr-select {
    width: 100%;
    max-width: 400px;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    transition: all 0.2s ease;
    color: #374151;
}

.cpr-input:focus, .cpr-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.cpr-input::placeholder {
    color: #9ca3af;
}

/* Input Group */
.cpr-input-group {
    display: flex;
    gap: 12px;
    align-items: center;
}

.cpr-input-group .cpr-input {
    flex: 1;
}

/* Toggle Switch moderno */
.cpr-toggle-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
}

.cpr-toggle {
    position: relative;
    display: inline-block;
    width: 48px;
    height: 24px;
}

.cpr-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.cpr-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #cbd5e1;
    transition: 0.3s;
    border-radius: 24px;
}

.cpr-toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.cpr-toggle input:checked + .cpr-toggle-slider {
    background-color: #3b82f6;
}

.cpr-toggle input:checked + .cpr-toggle-slider:before {
    transform: translateX(24px);
}

.cpr-toggle-text {
    font-size: 14px;
    color: #374151;
    font-weight: 500;
}

/* Conditional Fields */
.cpr-conditional {
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.cpr-conditional.show {
    display: block;
    opacity: 1;
}

/* Botões modernos */
.cpr-btn-primary, .cpr-btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
    border: none;
    outline: none;
}

.cpr-btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.cpr-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
    color: white;
}

.cpr-btn-secondary {
    background: white;
    color: #374151;
    border: 1px solid #d1d5db;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.cpr-btn-secondary:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    transform: translateY(-1px);
    color: #374151;
}

.cpr-btn-preview {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    outline: none;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.cpr-btn-preview:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);
    color: white;
}

.cpr-btn-icon {
    font-size: 16px;
    line-height: 1;
}

/* Action Buttons Container */
/* Action Buttons Container - Sticky apenas no admin */
.cpr-admin-actions {
    background: #ffffff !important;
    border-top: 1px solid #e5e5e5 !important;
    padding: 0 !important;
    position: sticky !important;
    bottom: 0 !important;
    z-index: 50 !important;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    transform: translateY(0) !important;
    width: 100% !important;
    left: auto !important;
    right: auto !important;
}

.cpr-actions-wrapper {
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding: 24px 40px !important;
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
}

/* Adicionar espaço no final da página para evitar sobreposição */
.cpr-main-content {
    padding-bottom: 120px !important;
}



/* Garantir que o container pai permita sticky apenas no admin */
.wrap.cpr-admin-wrap {
    height: auto !important;
    min-height: calc(100vh - 32px) !important;
    position: relative !important;
    overflow: visible !important;
}

.cpr-admin-container {
    min-height: calc(100vh - 200px) !important;
    padding-bottom: 100px !important;
}

/* Override WordPress admin styles que podem interferir */
.wrap .cpr-admin-actions {
    position: sticky !important;
    bottom: 0 !important;
}

/* CSS mais específico para forçar sticky */
body.wp-admin .wrap.cpr-admin-wrap .cpr-admin-actions {
    position: -webkit-sticky !important;
    position: sticky !important;
    bottom: 0 !important;
    z-index: 999999 !important;
}

/* Garantir que funcione mesmo com outros plugins */
#wpbody-content .wrap.cpr-admin-wrap .cpr-admin-actions {
    position: fixed !important;
    bottom: 0 !important;
}

/* Sticky apenas dentro do container do plugin */
.wrap.cpr-admin-wrap .cpr-admin-actions {
    position: sticky !important;
    bottom: 0 !important;
    z-index: 50 !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Garantir que o wrapper também seja sempre visível */
.cpr-actions-wrapper {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}



/* Status info styling */
.cpr-status-info {
    margin-left: auto !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    font-size: 14px !important;
    color: #6b7280 !important;
}

.cpr-status-label {
    font-weight: 500 !important;
}

.cpr-status-value {
    font-weight: 600 !important;
}

/* Color Picker */
.cpr-color-picker {
    max-width: 100px !important;
}

/* Preview Actions */
.cpr-preview-actions {
    display: flex;
    gap: 20px;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.cpr-test-email-wrapper {
    display: flex;
    gap: 12px;
    align-items: center;
}

.cpr-email-preview {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    min-height: 400px;
    padding: 20px;
}

.cpr-preview-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #6b7280;
}

.cpr-status-info {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.cpr-status-label {
    color: #6b7280;
    font-weight: 500;
}

.cpr-status-value {
    font-weight: 600;
}

/* Melhorar labels com ícones */
.cpr-setting-label {
    display: flex;
    align-items: center;
    gap: 8px;
}

.cpr-setting-label .dashicons {
    color: #6b7280;
    font-size: 16px;
}

/* Responsividade */
@media (max-width: 1200px) {
    .cpr-nav-wrapper,
    .cpr-tab-content,
    .cpr-actions-wrapper {
        padding-left: 30px;
        padding-right: 30px;
    }
}

@media (max-width: 768px) {
    .cpr-nav-wrapper {
        flex-direction: column;
        padding: 0;
    }

    .cpr-nav-tab {
        border-bottom: 1px solid #e2e8f0;
        border-right: none;
    }

    .cpr-tab-content {
        padding: 30px 20px;
    }

    .cpr-settings-grid {
        grid-template-columns: 1fr;
    }

    .cpr-preview-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .cpr-test-email-wrapper {
        justify-content: center;
    }

    .cpr-actions-wrapper {
        flex-direction: column;
        gap: 12px;
        padding: 20px !important;
    }

    .cpr-btn-primary,
    .cpr-btn-secondary,
    .cpr-btn-preview {
        width: 100%;
        justify-content: center;
    }

    .cpr-status-info {
        margin-left: 0 !important;
        justify-content: center;
        text-align: center;
        width: 100%;
        padding-top: 10px;
        border-top: 1px solid #e5e5e5;
    }

    /* Ajustar padding do conteúdo principal em mobile */
    .cpr-main-content {
        padding-bottom: 140px !important;
    }
}

.cpr-status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.cpr-status-label {
    font-size: 13px;
    color: #6c757d;
}

.cpr-status-value {
    font-size: 13px;
    font-weight: 600;
}

.cpr-info-item {
    margin-bottom: 15px;
    padding: 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.cpr-info-item strong {
    color: #2c3e50;
    display: block;
    margin-bottom: 5px;
}

.cpr-info-item code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    color: #e83e8c;
}

.cpr-preview-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    align-items: center;
}

.cpr-test-email-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.cpr-test-email-input {
    padding: 10px 15px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    width: 200px;
}

.cpr-email-preview {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    min-height: 400px;
    background: #f8f9fa;
    overflow: auto;
}

.cpr-preview-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    color: #6c757d;
}

.cpr-preview-icon {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.cpr-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cpr-loading-content {
    background: white;
    padding: 30px;
    border-radius: 8px;
    text-align: center;
}

.cpr-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.cpr-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 6px;
    color: white;
    font-weight: 600;
    z-index: 10000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
}

.cpr-notification.show {
    transform: translateX(0);
}

.cpr-notification-success {
    background: #28a745;
}

.cpr-notification-error {
    background: #dc3545;
}

@media (max-width: 1200px) {
    .cpr-container {
        flex-direction: column;
    }

    .cpr-sidebar {
        width: 100%;
        border-left: none;
        border-top: 1px solid #e9ecef;
    }

    .cpr-form-grid {
        grid-template-columns: 1fr;
    }
}

/* Modal de Preview */
.cpr-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 999999;
    display: none;
    align-items: center;
    justify-content: center;
}

.cpr-modal-overlay.show {
    display: flex;
}

.cpr-modal {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.cpr-modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
}

.cpr-modal-title {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.cpr-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.cpr-modal-close:hover {
    background: #e9ecef;
    color: #495057;
}

.cpr-modal-body {
    padding: 0;
    max-height: calc(90vh - 140px);
    overflow-y: auto;
}

.cpr-modal-preview-content {
    padding: 20px;
    background: #f8f9fa;
    min-height: 400px;
}

.cpr-modal-actions {
    padding: 15px 25px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.cpr-modal-btn {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: all 0.2s ease;
}

.cpr-modal-btn-primary {
    background: #3b82f6;
    color: white;
}

.cpr-modal-btn-primary:hover {
    background: #2563eb;
}

.cpr-modal-btn-secondary {
    background: #6c757d;
    color: white;
}

.cpr-modal-btn-secondary:hover {
    background: #5a6268;
}

/* Prevenir scroll quando modal estiver aberto */
body.modal-open {
    overflow: hidden;
}

/* Animação de spin para loading */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
</style>

<script>
jQuery(document).ready(function($) {
    'use strict';

    // Inicializar color pickers
    if ($.fn.wpColorPicker) {
        $('.cpr-color-picker').wpColorPicker();
    }

    // Navegação por abas
    $('.cpr-nav-tab').on('click', function(e) {
        e.preventDefault();

        const tabId = $(this).data('tab');

        // Remover classe ativa de todas as abas
        $('.cpr-nav-tab').removeClass('cpr-nav-tab-active');
        $('.cpr-tab-content').removeClass('active');

        // Adicionar classe ativa na aba clicada
        $(this).addClass('cpr-nav-tab-active');
        $('#tab-' + tabId).addClass('active');

        // Atualizar URL
        window.location.hash = tabId;
    });

    // Carregar aba da URL
    if (window.location.hash) {
        const hash = window.location.hash.substring(1);
        const $tab = $('[data-tab="' + hash + '"]');
        if ($tab.length) {
            $tab.trigger('click');
        }
    }

    // Toggle condicional
    $('#email_enabled').on('change', function() {
        const isEnabled = $(this).is(':checked');
        $('#status-email-enabled').text(isEnabled ? '✅ Ativo' : '❌ Inativo');
    });

    // Salvar configurações
    $('#save-settings').on('click', function(e) {
        e.preventDefault();

        const $button = $(this);
        const originalText = $button.html();

        $button.html('<span class="dashicons dashicons-update-alt"></span> Salvando...').prop('disabled', true);

        // Usar o método mais simples - serialize do formulário
        const formData = $('#cnue-settings-form').serialize();
        const postData = formData + '&action=cnue_save_settings&nonce=' + nonce;

        // Debug temporário
        console.log('Nonce:', nonce);
        console.log('Data:', postData);

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: postData,
            dataType: 'json',
            timeout: 10000,
            success: function(response) {
                console.log('Response:', response);
                if (response && response.success) {
                    showNotice('Configurações salvas com sucesso!', 'success');
                    $('#last-update').text('Agora mesmo');
                } else {
                    showNotice('Erro ao salvar: ' + (response.data || 'Resposta inválida'), 'error');
                }
            },
            error: function(xhr, status, error) {
                console.log('AJAX Error:', xhr.responseText);
                if (xhr.status === 403) {
                    showNotice('Erro de permissão (403). Recarregue a página e tente novamente.', 'error');
                } else {
                    showNotice('Erro de conexão: ' + status + ' - ' + error, 'error');
                }
            },
            complete: function() {
                $button.html(originalText).prop('disabled', false);
            }
        });
    });

    // Resetar configurações
    $('#reset-settings').on('click', function(e) {
        e.preventDefault();

        if (!confirm('Tem certeza que deseja resetar todas as configurações? Esta ação não pode ser desfeita.')) {
            return;
        }

        const $button = $(this);
        const originalText = $button.html();

        $button.html('<span class="dashicons dashicons-update-alt"></span> Resetando...').prop('disabled', true);

        $.post(ajaxurl, {
            action: 'cnue_reset_settings',
            nonce: $('[name="nonce"]').val()
        }, function(response) {
            if (response.success) {
                showNotice('Configurações resetadas com sucesso!', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotice('Erro ao resetar configurações: ' + response.data, 'error');
            }
        }).always(function() {
            $button.html(originalText).prop('disabled', false);
        });
    });

    // Função para mostrar notificações
    function showNotice(message, type) {
        const $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
        $('.cpr-admin-wrap').prepend($notice);

        setTimeout(() => {
            $notice.fadeOut(() => $notice.remove());
        }, 5000);
    }

    // Aplicar sticky apenas uma vez
    function applySticky() {
        const $actions = $('.cpr-admin-actions');
        if ($actions.length && !$actions.hasClass('sticky-applied')) {
            $actions.addClass('sticky-applied');
        }
    }

    // Aplicar sticky apenas uma vez
    applySticky();
});
</script>

<div class="wrap cpr-admin-wrap">
    <div class="cpr-admin-header">
        <div class="cpr-header-content">
            <div class="cpr-header-title">
                <div class="cpr-icon">
                    <i class="dashicons dashicons-email-alt"></i>
                </div>
                <div class="cpr-title-text">
                    <h1>Email Novo Usuário</h1>
                    <p>Personalize o email de criação de novo usuário do WordPress</p>
                </div>
            </div>
        </div>
    </div>

    <div class="cpr-admin-container cpr-main-content">
        <!-- Navegação por abas -->
        <nav class="cpr-nav-tabs">
            <div class="cpr-nav-wrapper">
                <a href="#configuracoes" class="cpr-nav-tab cpr-nav-tab-active" data-tab="configuracoes">
                    <span class="cpr-tab-icon dashicons dashicons-admin-generic"></span>
                    <span class="cpr-tab-text">Configurações de Email</span>
                </a>
                <a href="#preview" class="cpr-nav-tab" data-tab="preview">
                    <span class="cpr-tab-icon dashicons dashicons-visibility"></span>
                    <span class="cpr-tab-text">Preview & Teste</span>
                </a>
            </div>
        </nav>

        <!-- Formulário principal -->
        <form id="cnue-settings-form" method="post">
            <?php wp_nonce_field('cnue_admin_nonce', 'nonce'); ?>

            <!-- Aba Configurações -->
            <div class="cpr-tab-content active" id="tab-configuracoes">
                <div class="cpr-settings-grid">
                    <!-- Configurações Básicas -->
                    <div class="cpr-setting-group">
                        <div class="cpr-setting-header">
                            <h3><span class="dashicons dashicons-admin-settings"></span> Configurações Básicas</h3>
                        </div>

                        <div class="cpr-setting-item">
                            <label class="cpr-setting-label">
                                <span class="dashicons dashicons-yes-alt"></span>
                                Ativar Email Personalizado
                            </label>
                            <div class="cpr-toggle-wrapper">
                                <label class="cpr-toggle">
                                    <input type="checkbox" name="settings[email_enabled]" id="email_enabled" value="1" <?php checked($settings['email_enabled'], '1'); ?>>
                                    <span class="cpr-toggle-slider"></span>
                                </label>
                                <span class="cpr-toggle-text">Substituir o email padrão do WordPress</span>
                            </div>
                            <span class="cpr-setting-desc">Ative para usar o template personalizado e moderno</span>
                        </div>
                    </div>

                    <!-- Configurações do Email -->
                    <div class="cpr-setting-group">
                        <div class="cpr-setting-header">
                            <h3><span class="dashicons dashicons-email-alt"></span> Dados do Email</h3>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_subject" class="cpr-setting-label">
                                <span class="dashicons dashicons-format-status"></span>
                                Assunto do Email
                            </label>
                            <input type="text" name="settings[email_subject]" id="email_subject" value="<?php echo esc_attr($settings['email_subject']); ?>" class="cpr-input" placeholder="Bem-vindo! Seus dados de acesso">
                            <span class="cpr-setting-desc">Linha de assunto que o usuário verá na caixa de entrada</span>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_from_name" class="cpr-setting-label">
                                <span class="dashicons dashicons-admin-users"></span>
                                Nome do Remetente
                            </label>
                            <input type="text" name="settings[email_from_name]" id="email_from_name" value="<?php echo esc_attr($settings['email_from_name']); ?>" class="cpr-input" placeholder="<?php echo esc_attr(get_bloginfo('name')); ?>">
                            <span class="cpr-setting-desc">Nome que aparecerá como remetente</span>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_from_email" class="cpr-setting-label">
                                <span class="dashicons dashicons-email"></span>
                                Email do Remetente
                            </label>
                            <input type="email" name="settings[email_from_email]" id="email_from_email" value="<?php echo esc_attr($settings['email_from_email']); ?>" class="cpr-input" placeholder="<?php echo esc_attr(get_option('admin_email')); ?>">
                            <span class="cpr-setting-desc">Endereço de email que aparecerá como remetente</span>
                        </div>
                    </div>

                    <!-- Personalização Visual -->
                    <div class="cpr-setting-group">
                        <div class="cpr-setting-header">
                            <h3><span class="dashicons dashicons-admin-appearance"></span> Aparência do Email</h3>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_header_color" class="cpr-setting-label">
                                <span class="dashicons dashicons-art"></span>
                                Cor do Cabeçalho
                            </label>
                            <input type="text" name="settings[email_header_color]" id="email_header_color" value="<?php echo esc_attr($settings['email_header_color']); ?>" class="cpr-color-picker">
                            <span class="cpr-setting-desc">Cor de fundo do cabeçalho do email</span>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_header_pattern" class="cpr-setting-label">
                                <span class="dashicons dashicons-admin-customizer"></span>
                                Padrão Decorativo
                            </label>
                            <select name="settings[email_header_pattern]" id="email_header_pattern" class="cpr-select">
                                <option value="none" <?php selected($settings['email_header_pattern'], 'none'); ?>>Nenhum</option>
                                <option value="floating_dots" <?php selected($settings['email_header_pattern'], 'floating_dots'); ?>>Pontos Flutuantes</option>
                                <option value="organic_shapes" <?php selected($settings['email_header_pattern'], 'organic_shapes'); ?>>Formas Orgânicas</option>
                                <option value="geometric_minimal" <?php selected($settings['email_header_pattern'], 'geometric_minimal'); ?>>Geométrico Minimal</option>
                                <option value="flowing_lines" <?php selected($settings['email_header_pattern'], 'flowing_lines'); ?>>Linhas Fluidas</option>
                                <option value="scattered_elements" <?php selected($settings['email_header_pattern'], 'scattered_elements'); ?>>Elementos Espalhados</option>
                                <option value="modern_grid" <?php selected($settings['email_header_pattern'], 'modern_grid'); ?>>Grade Moderna</option>
                                <option value="abstract_art" <?php selected($settings['email_header_pattern'], 'abstract_art'); ?>>Arte Abstrata</option>
                            </select>
                            <span class="cpr-setting-desc">Padrão decorativo que aparecerá no fundo do cabeçalho</span>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_button_color" class="cpr-setting-label">
                                <span class="dashicons dashicons-button"></span>
                                Cor do Botão de Acesso
                            </label>
                            <input type="text" name="settings[email_button_color]" id="email_button_color" value="<?php echo esc_attr($settings['email_button_color']); ?>" class="cpr-color-picker">
                            <span class="cpr-setting-desc">Cor do botão "Acessar Minha Conta"</span>
                        </div>
                    </div>

                    <!-- Conteúdo do Email -->
                    <div class="cpr-setting-group">
                        <div class="cpr-setting-header">
                            <h3><span class="dashicons dashicons-edit"></span> Conteúdo da Mensagem</h3>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_greeting" class="cpr-setting-label">
                                <span class="dashicons dashicons-format-chat"></span>
                                Saudação Inicial
                            </label>
                            <input type="text" name="settings[email_greeting]" id="email_greeting" value="<?php echo esc_attr($settings['email_greeting']); ?>" class="cpr-input" placeholder="Olá">
                            <span class="cpr-setting-desc">Como cumprimentar o novo usuário</span>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_main_text" class="cpr-setting-label">
                                <span class="dashicons dashicons-text-page"></span>
                                Mensagem Principal
                            </label>
                            <textarea name="settings[email_main_text]" id="email_main_text" rows="3" class="cpr-input" placeholder="Sua conta foi criada com sucesso em {site_name}! Abaixo estão seus dados de acesso:"><?php echo esc_textarea($settings['email_main_text']); ?></textarea>
                            <span class="cpr-setting-desc">Mensagem de boas-vindas. Use <code>{site_name}</code> para incluir o nome do site</span>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_credentials_title" class="cpr-setting-label">
                                <span class="dashicons dashicons-lock"></span>
                                Título dos Dados de Acesso
                            </label>
                            <input type="text" name="settings[email_credentials_title]" id="email_credentials_title" value="<?php echo esc_attr($settings['email_credentials_title']); ?>" class="cpr-input" placeholder="Seus dados de acesso:">
                            <span class="cpr-setting-desc">Título que aparece antes dos dados de login</span>
                        </div>

                    </div>

                    <!-- Labels dos Dados de Acesso -->
                    <div class="cpr-setting-group">
                        <div class="cpr-setting-header">
                            <h3><span class="dashicons dashicons-admin-network"></span> Labels dos Dados</h3>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_username_label" class="cpr-setting-label">
                                <span class="dashicons dashicons-admin-users"></span>
                                Label do Nome de Usuário
                            </label>
                            <input type="text" name="settings[email_username_label]" id="email_username_label" value="<?php echo esc_attr($settings['email_username_label']); ?>" class="cpr-input" placeholder="Usuário:">
                            <span class="cpr-setting-desc">Texto que aparece antes do nome de usuário</span>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_password_label" class="cpr-setting-label">
                                <span class="dashicons dashicons-lock"></span>
                                Label da Senha
                            </label>
                            <input type="text" name="settings[email_password_label]" id="email_password_label" value="<?php echo esc_attr($settings['email_password_label']); ?>" class="cpr-input" placeholder="Senha:">
                            <span class="cpr-setting-desc">Texto que aparece antes da senha</span>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_login_url_label" class="cpr-setting-label">
                                <span class="dashicons dashicons-admin-links"></span>
                                Label do Link de Acesso
                            </label>
                            <input type="text" name="settings[email_login_url_label]" id="email_login_url_label" value="<?php echo esc_attr($settings['email_login_url_label']); ?>" class="cpr-input" placeholder="Link de acesso:">
                            <span class="cpr-setting-desc">Texto que aparece antes do link de login</span>
                        </div>
                    </div>

                    <!-- Textos Adicionais -->
                    <div class="cpr-setting-group">
                        <div class="cpr-setting-header">
                            <h3><span class="dashicons dashicons-format-quote"></span> Textos Complementares</h3>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_instruction_text" class="cpr-setting-label">
                                <span class="dashicons dashicons-info"></span>
                                Instruções de Acesso
                            </label>
                            <textarea name="settings[email_instruction_text]" id="email_instruction_text" rows="2" class="cpr-input" placeholder="Clique no botão abaixo para fazer login e começar a usar sua conta:"><?php echo esc_textarea($settings['email_instruction_text']); ?></textarea>
                            <span class="cpr-setting-desc">Instruções sobre como acessar a conta</span>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_button_text" class="cpr-setting-label">
                                <span class="dashicons dashicons-button"></span>
                                Texto do Botão
                            </label>
                            <input type="text" name="settings[email_button_text]" id="email_button_text" value="<?php echo esc_attr($settings['email_button_text']); ?>" class="cpr-input" placeholder="Acessar Minha Conta">
                            <span class="cpr-setting-desc">Texto que aparece no botão de acesso</span>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_security_note" class="cpr-setting-label">
                                <span class="dashicons dashicons-shield"></span>
                                Nota de Segurança
                            </label>
                            <textarea name="settings[email_security_note]" id="email_security_note" rows="2" class="cpr-input" placeholder="Por segurança, recomendamos que você altere sua senha após o primeiro login."><?php echo esc_textarea($settings['email_security_note']); ?></textarea>
                            <span class="cpr-setting-desc">Dica de segurança para o usuário</span>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_footer_text" class="cpr-setting-label">
                                <span class="dashicons dashicons-editor-help"></span>
                                Texto do Rodapé
                            </label>
                            <input type="text" name="settings[email_footer_text]" id="email_footer_text" value="<?php echo esc_attr($settings['email_footer_text']); ?>" class="cpr-input" placeholder="Este é um email automático, não responda.">
                            <span class="cpr-setting-desc">Texto que aparece no final do email</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Aba Preview & Teste -->
            <div class="cpr-tab-content" id="tab-preview">
                <div class="cpr-setting-group">
                    <div class="cpr-setting-header">
                        <h3><span class="dashicons dashicons-visibility"></span> Visualização e Teste</h3>
                    </div>

                    <div class="cpr-preview-actions">
                        <button type="button" id="generate-preview" class="cpr-btn-secondary">
                            <span class="dashicons dashicons-update"></span>
                            Gerar Preview
                        </button>

                        <div class="cpr-test-email-wrapper">
                            <input type="email" id="test-email" placeholder="<EMAIL>" class="cpr-input" style="max-width: 250px;">
                            <button type="button" id="send-test-email" class="cpr-btn-primary">
                                <span class="dashicons dashicons-email-alt"></span>
                                Enviar Teste
                            </button>
                        </div>
                    </div>

                    <div id="email-preview" class="cpr-email-preview">
                        <div class="cpr-preview-placeholder">
                            <div class="cpr-preview-icon">
                                <span class="dashicons dashicons-visibility" style="font-size: 48px; color: #ccc;"></span>
                            </div>
                            <p style="color: #666; text-align: center;">Clique em "Gerar Preview" para visualizar como ficará o email</p>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Action Buttons -->
    <div class="cpr-admin-actions">
        <div class="cpr-actions-wrapper">
            <button type="button" id="save-settings" class="cpr-btn-primary">
                <span class="dashicons dashicons-yes"></span>
                Salvar Configurações
            </button>
            <button type="button" id="reset-settings" class="cpr-btn-secondary">
                <span class="dashicons dashicons-update"></span>
                Resetar Configurações
            </button>
            <button type="button" id="preview-email-modal" class="cpr-btn-preview">
                <span class="dashicons dashicons-visibility"></span>
                Preview do Email
            </button>
            <div class="cpr-status-info">
                <span class="cpr-status-label">Status:</span>
                <span class="cpr-status-value" id="status-email-enabled">
                    <?php echo $settings['email_enabled'] ? '✅ Ativo' : '❌ Inativo'; ?>
                </span>
            </div>
        </div>
    </div>

    <!-- Modal de Preview -->
    <div id="email-preview-modal" class="cpr-modal-overlay">
        <div class="cpr-modal">
            <div class="cpr-modal-header">
                <h3 class="cpr-modal-title">
                    <span class="dashicons dashicons-visibility"></span>
                    Preview do Template de Email
                </h3>
                <button type="button" class="cpr-modal-close" id="close-preview-modal">
                    <span class="dashicons dashicons-no-alt"></span>
                </button>
            </div>
            <div class="cpr-modal-body">
                <div class="cpr-modal-preview-content" id="modal-email-preview">
                    <div class="cpr-preview-placeholder">
                        <div class="cpr-preview-icon">
                            <span class="dashicons dashicons-visibility" style="font-size: 48px; color: #ccc;"></span>
                        </div>
                        <p style="color: #666; text-align: center;">Carregando preview...</p>
                    </div>
                </div>
            </div>
            <div class="cpr-modal-actions">
                <button type="button" class="cpr-modal-btn cpr-modal-btn-secondary" id="refresh-preview">
                    <span class="dashicons dashicons-update"></span>
                    Atualizar Preview
                </button>
                <button type="button" class="cpr-modal-btn cpr-modal-btn-primary" id="close-modal-btn">
                    Fechar
                </button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="cpr-loading" class="cpr-loading" style="display: none;">
        <div class="cpr-loading-content">
            <div class="cpr-spinner"></div>
            <p>Processando...</p>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    const nonce = '<?php echo $nonce; ?>';
    
    // Inicializar color pickers
    $('.cpr-color-picker').wpColorPicker();
    
    // Navegação por abas
    $('.cpr-tab-button').on('click', function() {
        const tabId = $(this).data('tab');
        
        $('.cpr-tab-button').removeClass('active');
        $('.cpr-tab-panel').removeClass('active');
        
        $(this).addClass('active');
        $('#tab-' + tabId).addClass('active');
    });
    

    
    // Resetar configurações
    $('#reset-settings').on('click', function() {
        if (confirm('Tem certeza que deseja resetar todas as configurações? Esta ação não pode ser desfeita.')) {
            resetSettings();
        }
    });
    
    // Gerar preview
    $('#generate-preview').on('click', function() {
        generatePreview();
    });
    
    // Enviar email de teste
    $('#send-test-email').on('click', function() {
        sendTestEmail();
    });
    
    // Atualizar status quando checkbox mudar
    $('#email_enabled').on('change', function() {
        const isEnabled = $(this).is(':checked');
        $('#status-email-enabled').text(isEnabled ? '✅ Ativo' : '❌ Inativo');
    });

    // Modal de Preview
    $('#preview-email-modal').on('click', function() {
        openPreviewModal();
    });

    $('#close-preview-modal, #close-modal-btn').on('click', function() {
        closePreviewModal();
    });

    $('#refresh-preview').on('click', function() {
        generateModalPreview();
    });

    // Fechar modal ao clicar no overlay
    $('#email-preview-modal').on('click', function(e) {
        if (e.target === this) {
            closePreviewModal();
        }
    });

    // Fechar modal com ESC
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape' && $('#email-preview-modal').hasClass('show')) {
            closePreviewModal();
        }
    });
    
    function saveSettings(isPreview = false) {
        showLoading();
        
        const settings = {};
        $('input, select, textarea').each(function() {
            const name = $(this).attr('name');
            if (name) {
                if ($(this).attr('type') === 'checkbox') {
                    settings[name] = $(this).is(':checked') ? '1' : '0';
                } else {
                    settings[name] = $(this).val();
                }
            }
        });
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'cnue_save_settings',
                nonce: nonce,
                settings: settings,
                preview: isPreview ? '1' : '0'
            },
            success: function(response) {
                hideLoading();
                if (response.success) {
                    showNotification(response.data, 'success');
                    $('#last-update').text(new Date().toLocaleString('pt-BR'));
                } else {
                    showNotification('Erro ao salvar: ' + response.data, 'error');
                }
            },
            error: function() {
                hideLoading();
                showNotification('Erro de conexão', 'error');
            }
        });
    }
    
    function resetSettings() {
        showLoading();
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'cnue_reset_settings',
                nonce: nonce
            },
            success: function(response) {
                hideLoading();
                if (response.success) {
                    showNotification(response.data, 'success');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showNotification('Erro ao resetar: ' + response.data, 'error');
                }
            },
            error: function() {
                hideLoading();
                showNotification('Erro de conexão', 'error');
            }
        });
    }
    
    function generatePreview() {
        showLoading();
        
        const settings = {};
        $('input, select, textarea').each(function() {
            const name = $(this).attr('name');
            if (name) {
                if ($(this).attr('type') === 'checkbox') {
                    settings[name] = $(this).is(':checked') ? '1' : '0';
                } else {
                    settings[name] = $(this).val();
                }
            }
        });
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'cnue_generate_email_preview',
                nonce: nonce,
                settings: settings
            },
            success: function(response) {
                hideLoading();
                if (response.success) {
                    $('#email-preview').html(response.data);
                    showNotification('Preview gerado com sucesso!', 'success');
                } else {
                    showNotification('Erro ao gerar preview: ' + response.data, 'error');
                }
            },
            error: function() {
                hideLoading();
                showNotification('Erro de conexão', 'error');
            }
        });
    }
    
    function sendTestEmail() {
        const email = $('#test-email').val();
        if (!email) {
            showNotification('Por favor, insira um endereço de email', 'error');
            return;
        }
        
        showLoading();
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'cnue_send_test_email',
                nonce: nonce,
                email: email
            },
            success: function(response) {
                hideLoading();
                if (response.success) {
                    showNotification(response.data, 'success');
                } else {
                    showNotification('Erro ao enviar: ' + response.data, 'error');
                }
            },
            error: function() {
                hideLoading();
                showNotification('Erro de conexão', 'error');
            }
        });
    }
    
    function showLoading() {
        $('#cpr-loading').show();
    }
    
    function hideLoading() {
        $('#cpr-loading').hide();
    }
    
    function showNotification(message, type) {
        const notification = $('<div class="cpr-notification cpr-notification-' + type + '">' + message + '</div>');
        $('body').append(notification);

        setTimeout(() => {
            notification.addClass('show');
        }, 100);

        setTimeout(() => {
            notification.removeClass('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    // Funções do Modal de Preview
    function openPreviewModal() {
        $('#email-preview-modal').addClass('show');
        $('body').addClass('modal-open');
        generateModalPreview();
    }

    function closePreviewModal() {
        $('#email-preview-modal').removeClass('show');
        $('body').removeClass('modal-open');
    }

    function generateModalPreview() {
        const settings = {};
        $('input, select, textarea').each(function() {
            const name = $(this).attr('name');
            if (name) {
                if ($(this).attr('type') === 'checkbox') {
                    settings[name] = $(this).is(':checked') ? '1' : '0';
                } else {
                    settings[name] = $(this).val();
                }
            }
        });

        // Mostrar loading no modal
        $('#modal-email-preview').html(`
            <div class="cpr-preview-placeholder">
                <div class="cpr-preview-icon">
                    <span class="dashicons dashicons-update-alt" style="font-size: 48px; color: #ccc; animation: spin 1s linear infinite;"></span>
                </div>
                <p style="color: #666; text-align: center;">Gerando preview...</p>
            </div>
        `);

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'cnue_generate_email_preview',
                nonce: nonce,
                settings: settings
            },
            success: function(response) {
                if (response.success) {
                    $('#modal-email-preview').html(response.data);
                } else {
                    $('#modal-email-preview').html(`
                        <div class="cpr-preview-placeholder">
                            <div class="cpr-preview-icon">
                                <span class="dashicons dashicons-warning" style="font-size: 48px; color: #dc3545;"></span>
                            </div>
                            <p style="color: #dc3545; text-align: center;">Erro ao gerar preview: ${response.data}</p>
                        </div>
                    `);
                }
            },
            error: function() {
                $('#modal-email-preview').html(`
                    <div class="cpr-preview-placeholder">
                        <div class="cpr-preview-icon">
                            <span class="dashicons dashicons-warning" style="font-size: 48px; color: #dc3545;"></span>
                        </div>
                        <p style="color: #dc3545; text-align: center;">Erro de conexão</p>
                    </div>
                `);
            }
        });
    }
});
</script>
